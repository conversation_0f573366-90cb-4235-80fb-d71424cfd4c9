Config = {}

-- <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> türü: 'qb-target' veya 'interact'
Config.InteractionType = 'qb-target' -- 'qb-target' veya 'interact'

-- Komut Ayarları
Config.Commands = {
    checkTime = 'rentaltime' -- <PERSON><PERSON> süreyi kontrol etme komutu
}

-- <PERSON>ç <PERSON>a Lokasyonları
Config.RentalLocations = {
    -- LSIA Havaalanı
    {
        id = 'lsia',
        label = 'LSIA Araç Kiralama',
        logo = 'lsia_logo.svg', -- Lokasyon logosu (images/ klasöründen otomatik çekilir)
        uiTheme = {
            primaryColor = 'blue' -- 'blue', 'green', 'purple', 'red', 'orange', 'pink', 'indigo'
        },
        rentalSettings = {
            defaultTime = 30, -- Dakika
            maxTime = 120, -- <PERSON><PERSON><PERSON><PERSON> kiralama süresi (dakika)
            minTime = 1, -- Minimum kiralama süresi (dakika)
            deleteOnExpire = true, -- <PERSON><PERSON><PERSON> bitince aracı sil
            warningTime = 5 -- <PERSON><PERSON> dakika kala uyarı verilecek
        },
        npc = {
            enabled = true,
            model = 'a_m_m_business_01',
            coords = vector4(-1037.58, -2730.67, 20.17, 240.0),
            scenario = 'WORLD_HUMAN_CLIPBOARD'
        },
        spawnPoint = vector4(-1034.59, -2728.0, 19.66, 239.3),
        blip = {
            enabled = true,
            sprite = 225,
            color = 5,
            scale = 0.8,
            label = 'LSIA Araç Kiralama'
        },
        cars = {
            {
                model = 'sultan',
                label = 'Sultan',
                price = 100,
                category = 'Sedan',
                icon = 'fas fa-car' -- Araç ikonu
            },
            {
                model = 'elegy2',
                label = 'Elegy RH8',
                price = 150,
                category = 'Sports',
                icon = 'fas fa-car-side'
            },
            {
                model = 'adder',
                label = 'Adder',
                price = 300,
                category = 'Super',
                icon = 'fas fa-fighter-jet'
            }
        }
    },

    -- Şehir Merkezi
    {
        id = 'downtown',
        label = 'Downtown Araç Kiralama',
        logo = 'downtown_logo.svg', -- Lokasyon logosu
        uiTheme = {
            primaryColor = 'green' -- Yeşil tema
        },
        rentalSettings = {
            defaultTime = 45, -- Daha uzun varsayılan süre
            maxTime = 180, -- Daha uzun maksimum süre
            minTime = 5,
            deleteOnExpire = true,
            warningTime = 10 -- Daha erken uyarı
        },
        npc = {
            enabled = true,
            model = 'a_f_y_business_02',
            coords = vector4(-203.72, -1327.66, 30.89, 270.0),
            scenario = 'WORLD_HUMAN_STAND_MOBILE'
        },
        spawnPoint = vector4(-210.72, -1327.66, 30.89, 180.0),
        blip = {
            enabled = true,
            sprite = 225,
            color = 3,
            scale = 0.8,
            label = 'Downtown Araç Kiralama'
        },
        cars = {
            {
                model = 'sultan',
                label = 'Sultan',
                price = 120,
                category = 'Sedan',
                icon = 'fas fa-car'
            },
            {
                model = 'bison',
                label = 'Bison',
                price = 90,
                category = 'Van',
                icon = 'fas fa-bus'
            },
            {
                model = 'sandking',
                label = 'Sandking XL',
                price = 140,
                category = 'SUV',
                icon = 'fas fa-truck'
            }
        }
    },

    -- Sandy Shores
    {
        id = 'sandy',
        label = 'Sandy Shores Araç Kiralama',
        logo = 'sandy_logo.svg', -- Lokasyon logosu
        uiTheme = {
            primaryColor = 'orange' -- Turuncu tema (çöl teması)
        },
        rentalSettings = {
            defaultTime = 60, -- Kırsal alan için uzun süre
            maxTime = 240, -- Çok uzun maksimum süre
            minTime = 10,
            deleteOnExpire = true,
            warningTime = 15 -- Çok erken uyarı
        },
        npc = {
            enabled = true,
            model = 'a_m_m_hillbilly_01',
            coords = vector4(1737.59, 3710.2, 34.14, 20.0),
            scenario = 'WORLD_HUMAN_SMOKING'
        },
        spawnPoint = vector4(1740.59, 3715.2, 34.14, 110.0),
        blip = {
            enabled = true,
            sprite = 225,
            color = 17,
            scale = 0.8,
            label = 'Sandy Shores Araç Kiralama'
        },
        cars = {
            {
                model = 'sandking',
                label = 'Sandking XL',
                price = 100,
                category = 'SUV',
                icon = 'fas fa-truck'
            },
            {
                model = 'bison',
                label = 'Bison',
                price = 70,
                category = 'Van',
                icon = 'fas fa-bus'
            },
            {
                model = 'sultan',
                label = 'Sultan',
                price = 80,
                category = 'Sedan',
                icon = 'fas fa-car'
            }
        }
    }
}