Config = {}

-- Locale Ayarları
Config.Locale = 'en' -- Varsay<PERSON>lan dil ('tr', 'en')



-- <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> türü: 'qb-target' veya 'interact'
Config.InteractionType = 'qb-target' -- 'qb-target' veya 'interact'

-- <PERSON><PERSON>t Ayarları
Config.Commands = {
    checkTime = 'rentaltime' -- <PERSON><PERSON> süreyi kontrol etme komutu
}

-- <PERSON>ç Kiralama Lokasyonları
Config.RentalLocations = {
    -- LSIA Havaalanı
    {
        id = 'lsia',
        label = 'LSIA Araç Kiralama',
        logo = 'lsia_logo.svg', -- Lokasyon logosu (images/ klasöründen otomatik çekilir)
        uiTheme = {
            primaryColor = 'blue' -- 'blue', 'green', 'purple', 'red', 'orange', 'pink', 'indigo'
        },
        rentalSettings = {
            defaultTime = 30, -- Dakika
            maxTime = 120, -- <PERSON><PERSON><PERSON><PERSON> kiralama süresi (dakika)
            minTime = 1, -- Minimum kiralama süresi (dakika)
            deleteOnExpire = true, -- <PERSON><PERSON>re bitince aracı sil
            warningTime = 5 -- <PERSON><PERSON> dakika kala uyarı verilecek
        },
        npc = {
            enabled = true,
            model = 'a_m_m_business_01',
            coords = vector4(-1037.58, -2730.67, 20.17, 240.0),
            scenario = 'WORLD_HUMAN_CLIPBOARD'
        },
        spawnPoint = vector4(-1034.59, -2728.0, 19.66, 239.3),
        blip = {
            enabled = true,
            sprite = 225,
            color = 5,
            scale = 0.8,
            label = 'LSIA Araç Kiralama'
        },
        cars = {
            {
                model = 'sultan',
                label = 'Sultan',
                price = 100,
                category = 'Sedan',
                icon = 'fas fa-car' -- Araç ikonu
            },
            {
                model = 'elegy2',
                label = 'Elegy RH8',
                price = 150,
                category = 'Sports',
                icon = 'fas fa-car-side'
            },
            {
                model = 'adder',
                label = 'Adder',
                price = 300,
                category = 'Super',
                icon = 'fas fa-fighter-jet'
            }
        }
    },

    -- Şehir Merkezi
    {
        id = 'downtown',
        label = 'Downtown Araç Kiralama',
        logo = 'downtown_logo.svg', -- Lokasyon logosu
        uiTheme = {
            primaryColor = 'green' -- Yeşil tema
        },
        rentalSettings = {
            defaultTime = 45, -- Daha uzun varsayılan süre
            maxTime = 180, -- Daha uzun maksimum süre
            minTime = 5,
            deleteOnExpire = true,
            warningTime = 10 -- Daha erken uyarı
        },
        npc = {
            enabled = true,
            model = 'a_f_y_business_02',
            coords = vector4(-203.72, -1327.66, 30.89, 270.0),
            scenario = 'WORLD_HUMAN_STAND_MOBILE'
        },
        spawnPoint = vector4(-210.72, -1327.66, 30.89, 180.0),
        blip = {
            enabled = true,
            sprite = 225,
            color = 3,
            scale = 0.8,
            label = 'Downtown Araç Kiralama'
        },
        cars = {
            {
                model = 'sultan',
                label = 'Sultan',
                price = 120,
                category = 'Sedan',
                icon = 'fas fa-car'
            },
            {
                model = 'bison',
                label = 'Bison',
                price = 90,
                category = 'Van',
                icon = 'fas fa-bus'
            },
            {
                model = 'sandking',
                label = 'Sandking XL',
                price = 140,
                category = 'SUV',
                icon = 'fas fa-truck'
            }
        }
    },

    -- Sandy Shores
    {
        id = 'sandy',
        label = 'Sandy Shores Araç Kiralama',
        logo = 'sandy_logo.svg', -- Lokasyon logosu
        uiTheme = {
            primaryColor = 'orange' -- Turuncu tema (çöl teması)
        },
        rentalSettings = {
            defaultTime = 60, -- Kırsal alan için uzun süre
            maxTime = 240, -- Çok uzun maksimum süre
            minTime = 10,
            deleteOnExpire = true,
            warningTime = 15 -- Çok erken uyarı
        },
        npc = {
            enabled = true,
            model = 'a_m_m_hillbilly_01',
            coords = vector4(1737.59, 3710.2, 34.14, 20.0),
            scenario = 'WORLD_HUMAN_SMOKING'
        },
        spawnPoint = vector4(1740.59, 3715.2, 34.14, 110.0),
        blip = {
            enabled = true,
            sprite = 225,
            color = 17,
            scale = 0.8,
            label = 'Sandy Shores Araç Kiralama'
        },
        cars = {
            {
                model = 'sandking',
                label = 'Sandking XL',
                price = 100,
                category = 'SUV',
                icon = 'fas fa-truck'
            },
            {
                model = 'bison',
                label = 'Bison',
                price = 70,
                category = 'Van',
                icon = 'fas fa-bus'
            },
            {
                model = 'sultan',
                label = 'Sultan',
                price = 80,
                category = 'Sedan',
                icon = 'fas fa-car'
            }
        }
    }
}

-- Çeviriler
Config.Locales = {
    ['tr'] = {
        -- Genel
        ['car_rental'] = 'Araç Kiralama',
        ['time_selection'] = 'Süre Seçimi',
        ['category'] = 'Kategori',
        ['price'] = 'Fiyat',
        ['total_price'] = 'Toplam Fiyat',
        ['rental_time_minutes'] = 'Kiralama Süresi (Dakika)',
        ['enter_time'] = 'Süre girin...',
        ['rent'] = 'Kirala',
        ['no_cars_found'] = 'Araç bulunamadı',

        -- Bildirimler
        ['location_not_found'] = 'Lokasyon bulunamadı!',
        ['invalid_location'] = 'Geçersiz lokasyon!',
        ['car_rented_success'] = '%s başarıyla kiralandı! Süre: %d dakika',
        ['rental_warning'] = 'Kiralama süreniz %d dakika içinde bitecek!',
        ['rental_expired_deleted'] = 'Kiralama süreniz doldu, araç geri alındı.',
        ['rental_expired'] = 'Kiralama süreniz doldu!',
        ['no_rental_car'] = 'Şu anda kiralık aracınız bulunmuyor.',
        ['rental_car_not_found'] = 'Kiralık aracınız bulunamadı.',
        ['rental_time_expired'] = 'Kiralama süreniz dolmuş.',
        ['remaining_time'] = 'Kalan süre: %d dakika %d saniye',

        -- Hatalar
        ['player_data_not_found'] = 'Oyuncu verisi bulunamadı!',
        ['invalid_location_server'] = 'Geçersiz lokasyon!',
        ['rental_time_invalid'] = 'Kiralama süresi %d-%d dakika arasında olmalıdır!',
        ['already_have_rental'] = 'Zaten kiralık bir aracınız var!',
        ['not_enough_money'] = 'Yeterli paranız yok!',
        ['money_remove_failed'] = 'Para çekme işlemi başarısız!',

        -- Komutlar
        ['no_permission'] = 'Bu komutu kullanma yetkiniz yok!',
        ['total_rentals_found'] = 'Toplam %d aktif kiralama bulundu. Konsolu kontrol edin.',

        -- TextUI
        ['press_e_interact'] = '[E] %s',

        -- Dakika kısaltması
        ['minute_short'] = 'dk',
    },

    ['en'] = {
        -- General
        ['car_rental'] = 'Car Rental',
        ['time_selection'] = 'Time Selection',
        ['category'] = 'Category',
        ['price'] = 'Price',
        ['total_price'] = 'Total Price',
        ['rental_time_minutes'] = 'Rental Time (Minutes)',
        ['enter_time'] = 'Enter time...',
        ['rent'] = 'Rent',
        ['no_cars_found'] = 'No cars found',

        -- Notifications
        ['location_not_found'] = 'Location not found!',
        ['invalid_location'] = 'Invalid location!',
        ['car_rented_success'] = '%s successfully rented! Duration: %d minutes',
        ['rental_warning'] = 'Your rental will expire in %d minutes!',
        ['rental_expired_deleted'] = 'Your rental has expired, vehicle has been returned.',
        ['rental_expired'] = 'Your rental has expired!',
        ['no_rental_car'] = 'You currently have no rental car.',
        ['rental_car_not_found'] = 'Your rental car was not found.',
        ['rental_time_expired'] = 'Your rental time has expired.',
        ['remaining_time'] = 'Remaining time: %d minutes %d seconds',

        -- Errors
        ['player_data_not_found'] = 'Player data not found!',
        ['invalid_location_server'] = 'Invalid location!',
        ['rental_time_invalid'] = 'Rental time must be between %d-%d minutes!',
        ['already_have_rental'] = 'You already have a rental car!',
        ['not_enough_money'] = 'Not enough money!',
        ['money_remove_failed'] = 'Money removal failed!',

        -- Commands
        ['no_permission'] = 'You do not have permission to use this command!',
        ['total_rentals_found'] = 'Total %d active rentals found. Check console.',

        -- TextUI
        ['press_e_interact'] = '[E] %s',

        -- Dakika kısaltması
        ['minute_short'] = 'min',
    }
}