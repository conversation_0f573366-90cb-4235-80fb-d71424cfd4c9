local QBCore = exports['qb-core']:GetCoreObject()

-- Güvenlik kontrolü
local function lotus_SecurityCheck()
    local expectedResourceName = "lotus_carrent"
    local currentResourceName = GetCurrentResourceName()

    if currentResourceName ~= expectedResourceName then
        print("^1[LOTUS SECURITY]^7 ⚠️  GÜVENLIK İHLALİ TESPİT EDİLDİ!")
        print("^1[LOTUS SECURITY]^7 📁 Beklenen klasör adı: " .. expectedResourceName)
        print("^1[LOTUS SECURITY]^7 📁 Mevcut klasör adı: " .. currentResourceName)
        print("^1[LOTUS SECURITY]^7 🚫 Script güvenlik nedeniyle devre dışı bırakıldı!")
        print("^1[LOTUS SECURITY]^7 💡 Lütfen klasör adını '" .. expectedResourceName .. "' olarak değiştirin.")

        -- Script'i durdur
        StopResource(currentResourceName)
        return false
    end

    print("^2[LOTUS SECURITY]^7 ✅ Güvenlik kontrolü başarılı - Script yetkilendirildi")
    return true
end

-- Güvenlik kontrolünü başlat
if not lotus_SecurityCheck() then
    return -- Script'i durdur
end

-- Locale fonksiyonu
local function _L(key, ...)
    local locale = Locales[Config.Locale]
    if locale and locale[key] then
        if ... then
            return string.format(locale[key], ...)
        else
            return locale[key]
        end
    else
        return key -- Fallback olarak key'i döndür
    end
end

-- Aktif kiralamalar
local activeRentals = {}

-- Araç kiralama eventi
RegisterNetEvent('carrent:server:rentCar', function(carData, rentalTime, totalPrice, locationId)
    local src = source
    local Player = QBCore.Functions.GetPlayer(src)

    if not Player then
        TriggerClientEvent('carrent:client:rentalFailed', src, _L('player_data_not_found'))
        return
    end

    -- Find location to get rental settings
    local location = nil
    for _, loc in pairs(Config.RentalLocations) do
        if loc.id == locationId then
            location = loc
            break
        end
    end

    if not location then
        TriggerClientEvent('carrent:client:rentalFailed', src, _L('invalid_location_server'))
        return
    end

    -- Validate rental time against location settings
    if rentalTime < location.rentalSettings.minTime or rentalTime > location.rentalSettings.maxTime then
        TriggerClientEvent('carrent:client:rentalFailed', src, _L('rental_time_invalid',
            location.rentalSettings.minTime, location.rentalSettings.maxTime))
        return
    end

    -- Oyuncunun zaten kiralık aracı var mı kontrol et
    if activeRentals[Player.PlayerData.citizenid] then
        TriggerClientEvent('carrent:client:rentalFailed', src, _L('already_have_rental'))
        return
    end

    -- Para kontrolü
    if Player.PlayerData.money.cash < totalPrice then
        if Player.PlayerData.money.bank < totalPrice then
            TriggerClientEvent('carrent:client:rentalFailed', src, _L('not_enough_money'))
            return
        else
            -- Bankadan çek
            if not Player.Functions.RemoveMoney('bank', totalPrice, 'car-rental') then
                TriggerClientEvent('carrent:client:rentalFailed', src, _L('money_remove_failed'))
                return
            end
        end
    else
        -- Nakit olarak öde
        if not Player.Functions.RemoveMoney('cash', totalPrice, 'car-rental') then
            TriggerClientEvent('carrent:client:rentalFailed', src, _L('money_remove_failed'))
            return
        end
    end

    -- Kiralama kaydını oluştur
    activeRentals[Player.PlayerData.citizenid] = {
        carModel = carData.model,
        startTime = os.time(),
        endTime = os.time() + (rentalTime * 60),
        rentalTime = rentalTime,
        price = totalPrice,
        locationId = locationId
    }

    -- Aracı spawn et
    TriggerClientEvent('carrent:client:spawnCar', src, carData, rentalTime, locationId, location.rentalSettings)

    -- Log kaydı
    print(string.format('[CarRent] %s (%s) rented %s for %d minutes ($%d) at %s',
        Player.PlayerData.charinfo.firstname .. ' ' .. Player.PlayerData.charinfo.lastname,
        Player.PlayerData.citizenid,
        carData.label,
        rentalTime,
        totalPrice,
        locationId
    ))

    -- Kiralama süresinin sonunda temizlik yap (location settings kullanarak)
    SetTimeout(rentalTime * 60 * 1000, function()
        if activeRentals[Player.PlayerData.citizenid] then
            activeRentals[Player.PlayerData.citizenid] = nil
        end
    end)
end)

-- Oyuncu çıktığında temizlik
RegisterNetEvent('QBCore:Server:OnPlayerUnload', function(src)
    local Player = QBCore.Functions.GetPlayer(src)
    if Player and activeRentals[Player.PlayerData.citizenid] then
        activeRentals[Player.PlayerData.citizenid] = nil
    end
end)

-- Kalan süreyi kontrol etme komutu (sunucu tarafı)
QBCore.Commands.Add(Config.Commands.checkTime, 'Kiralık araç sürenizi kontrol edin', {}, false, function(source, args)
    local Player = QBCore.Functions.GetPlayer(source)
    if not Player then return end
    
    local rental = activeRentals[Player.PlayerData.citizenid]
    if not rental then
        TriggerClientEvent('QBCore:Notify', source, _L('no_rental_car'), 'error')
        return
    end

    local currentTime = os.time()
    local remainingTime = rental.endTime - currentTime

    if remainingTime <= 0 then
        activeRentals[Player.PlayerData.citizenid] = nil
        TriggerClientEvent('QBCore:Notify', source, _L('rental_time_expired'), 'error')
        return
    end

    local minutes = math.floor(remainingTime / 60)
    local seconds = remainingTime % 60

    TriggerClientEvent('QBCore:Notify', source, _L('remaining_time', minutes, seconds), 'primary')
end)

-- Admin komutu - tüm kiralamaları göster
QBCore.Commands.Add('rentaladmin', 'Tüm aktif kiralamaları göster (Admin)', {}, false, function(source, args)
    local Player = QBCore.Functions.GetPlayer(source)
    if not Player then return end
    
    -- Admin kontrolü
    if not QBCore.Functions.HasPermission(source, 'admin') then
        TriggerClientEvent('QBCore:Notify', source, _L('no_permission'), 'error')
        return
    end
    
    local rentalCount = 0
    for citizenid, rental in pairs(activeRentals) do
        rentalCount = rentalCount + 1
        local remainingTime = rental.endTime - os.time()
        local minutes = math.floor(remainingTime / 60)
        local seconds = remainingTime % 60
        
        print(string.format('[CarRent Admin] CitizenID: %s | Model: %s | Remaining: %dm %ds', 
            citizenid, rental.carModel, minutes, seconds))
    end
    
    TriggerClientEvent('QBCore:Notify', source, _L('total_rentals_found', rentalCount), 'success')
end, 'admin')

-- Kaynak durdurulduğunda temizlik
AddEventHandler('onResourceStop', function(resource)
    if resource == GetCurrentResourceName() then
        activeRentals = {}
        print('[CarRent] Resource stopped, all rentals cleared.')
    end
end)

-- Kaynak başlatıldığında
AddEventHandler('onResourceStart', function(resource)
    if resource == GetCurrentResourceName() then
        print('[CarRent] Resource started successfully!')

        -- Count total cars across all locations
        local totalCars = 0
        for _, location in pairs(Config.RentalLocations) do
            totalCars = totalCars + #location.cars
        end

        print('[CarRent] Total locations: ' .. #Config.RentalLocations)
        print('[CarRent] Total available cars: ' .. totalCars)
        print('[CarRent] Interaction type: ' .. Config.InteractionType)
        print('[CarRent] Check time command: /' .. Config.Commands.checkTime)
    end
end)