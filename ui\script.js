let currentCar = null;

// Default car icon (fallback)
const defaultCarIcon = 'fas fa-car';

// Theme management
let currentTheme = 'light';
let currentColor = 'blue';
let currentLocation = null;

// Locale data
let localeData = null;

// Locale function
function _L(key, ...args) {
    if (localeData && localeData[key]) {
        if (args.length > 0) {
            let text = localeData[key];
            // Replace %s, %d patterns with arguments
            for (let i = 0; i < args.length; i++) {
                text = text.replace(/%[sd]/, args[i]);
            }
            return text;
        }
        return localeData[key];
    }
    return key; // Fallback to key if translation not found
}

// Update UI texts with locale
function updateUITexts() {
    if (!localeData) return;

    // Update static texts
    document.getElementById('carMenuTitle').textContent = _L('car_rental');
    document.getElementById('timeMenuTitle').textContent = _L('time_selection');
    document.getElementById('rentalTimeLabel').textContent = _L('rental_time_minutes');
    document.getElementById('totalPriceLabel').textContent = _L('total_price') + ': ';
    document.getElementById('rentBtn').textContent = _L('rent');

    // Update placeholder
    const rentalTimeInput = document.getElementById('rentalTime');
    if (rentalTimeInput) {
        rentalTimeInput.placeholder = _L('enter_time');
    }
}

function toggleTheme() {
    currentTheme = currentTheme === 'light' ? 'dark' : 'light';
    updateThemeAndColor();

    const themeToggles = document.querySelectorAll('.theme-toggle');
    themeToggles.forEach(toggle => {
        const icon = toggle.querySelector('i');
        if (currentTheme === 'light') {
            icon.className = 'fas fa-moon';
        } else {
            icon.className = 'fas fa-sun';
        }
    });
}

function setColor(color) {
    currentColor = color;
    updateThemeAndColor();
}

function updateThemeAndColor() {
    const menus = document.querySelectorAll('.menu');

    menus.forEach(menu => {
        menu.setAttribute('data-theme', currentTheme);
        menu.setAttribute('data-color', currentColor);
    });

    // Update root element for global color variables
    document.documentElement.setAttribute('data-color', currentColor);
    document.documentElement.setAttribute('data-theme', currentTheme);
}

document.addEventListener('DOMContentLoaded', function() {
    const closeBtn = document.getElementById('closeBtn');
    const backBtn = document.getElementById('backBtn');
    const rentBtn = document.getElementById('rentBtn');
    const rentalTime = document.getElementById('rentalTime');
    const totalPrice = document.getElementById('totalPrice');
    const themeToggle = document.getElementById('themeToggle');
    const themeToggle2 = document.getElementById('themeToggle2');

    closeBtn.addEventListener('click', closeMenu);
    backBtn.addEventListener('click', showCarMenu);
    rentBtn.addEventListener('click', rentCar);
    rentalTime.addEventListener('input', updateTotalPrice);
    themeToggle.addEventListener('click', toggleTheme);
    themeToggle2.addEventListener('click', toggleTheme);

    // Initialize theme and color
    updateThemeAndColor();
    
    window.addEventListener('message', function(event) {
        const data = event.data;

        try {
            if (data.action === 'toggleMenu') {
                if (data.show) {
                    document.body.style.display = 'flex';
                    // Add entrance animation
                    setTimeout(() => {
                        const menu = document.querySelector('.menu[style*="block"], .menu:not([style*="none"])');
                        if (menu) {
                            menu.style.animation = 'slideInRight 0.3s cubic-bezier(0.16, 1, 0.3, 1)';
                        }
                    }, 10);
                } else {
                    document.body.style.display = 'none';
                }
            }
            else if (data.action === 'showCarMenu') {
                // Set locale data if provided
                if (data.locale) {
                    localeData = data.locale;
                    updateUITexts();
                }

                // Set theme and color from location config
                if (data.location && data.location.uiTheme) {
                    setColor(data.location.uiTheme.primaryColor || 'blue');
                }

                // Set current location and rental settings
                if (data.location) {
                    currentLocation = data.location;
                    // Update header with location name
                    document.querySelector('#carMenu h1').textContent = data.location.label;

                    // Update location logo
                    const locationLogo = document.getElementById('locationLogo');
                    if (locationLogo && data.location.logo) {
                        console.log('Loading logo:', data.location.logo);
                        locationLogo.src = data.location.logo;
                        locationLogo.onload = function() {
                            console.log('Logo loaded successfully:', data.location.logo);
                            this.classList.add('loaded');
                        };
                        locationLogo.onerror = function() {
                            console.error('Failed to load logo:', data.location.logo);
                            // Hide logo if it fails to load
                            this.style.display = 'none';
                        };
                    } else {
                        // Hide logo if no logo is configured
                        if (locationLogo) {
                            locationLogo.style.display = 'none';
                        }
                    }

                    // Update rental time input with location settings
                    const rentalTimeInput = document.getElementById('rentalTime');
                    if (rentalTimeInput && data.location.rentalSettings) {
                        rentalTimeInput.value = data.location.rentalSettings.defaultTime;
                        rentalTimeInput.min = data.location.rentalSettings.minTime;
                        rentalTimeInput.max = data.location.rentalSettings.maxTime;
                    }
                }

                showCarMenu(data.cars);
            }
            else if (data.action === 'showTimeMenu') {
                showTimeMenu(data.car);
            }
        } catch (error) {
            console.error('Error handling message:', error, data);
        }
    });
});

function closeMenu() {
    fetch(`https://${GetParentResourceName()}/closeMenu`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({})
    }).catch(error => {
        console.error('Error closing menu:', error);
    });
}

function showCarMenu(cars) {
    console.log('showCarMenu called with:', cars);

    const carMenu = document.getElementById('carMenu');
    const timeMenu = document.getElementById('timeMenu');

    carMenu.style.display = 'block';
    timeMenu.style.display = 'none';

    // Sync theme and color
    carMenu.setAttribute('data-theme', currentTheme);
    carMenu.setAttribute('data-color', currentColor);
    timeMenu.setAttribute('data-theme', currentTheme);
    timeMenu.setAttribute('data-color', currentColor);

    // If cars data is provided, use it directly
    if (cars && Array.isArray(cars)) {
        const carList = document.getElementById('carList');
        carList.innerHTML = '';

        if (cars.length === 0) {
            carList.innerHTML = `<div style="text-align: center; padding: 20px; color: #a1a1aa;">${_L('no_cars_found')}</div>`;
            return;
        }

        cars.forEach((car, index) => {
            const carItem = document.createElement('div');
            carItem.className = 'car-item';
            carItem.style.opacity = '0';
            carItem.style.transform = 'translateY(20px)';
            carItem.innerHTML = `
                <div class="car-icon">
                    <i class="${car.icon || defaultCarIcon}"></i>
                </div>
                <div class="car-info">
                    <h3>${car.label}</h3>
                    <p>${car.category} | $${car.price}/${_L('minute_short')}</p>
                </div>
                <div class="price-tag">$${car.price}/${_L('minute_short')}</div>
            `;

            carItem.addEventListener('click', () => {
                currentCar = car;
                showTimeMenu(car);
            });

            carList.appendChild(carItem);

            // Animate in with delay
            setTimeout(() => {
                carItem.style.transition = 'all 0.4s cubic-bezier(0.16, 1, 0.3, 1)';
                carItem.style.opacity = '1';
                carItem.style.transform = 'translateY(0)';
            }, index * 100 + 100);
        });
    } else {
        console.log('No cars data provided, requesting from client');
        // Fallback: request cars from client
        fetch(`https://${GetParentResourceName()}/showCarMenu`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({})
        }).catch(error => {
            console.error('Error requesting cars:', error);
        });
    }
}

function showTimeMenu(car) {
    console.log('showTimeMenu called with:', car);

    if (!car) {
        console.error('No car data provided to showTimeMenu');
        return;
    }

    const carMenu = document.getElementById('carMenu');
    const timeMenu = document.getElementById('timeMenu');

    carMenu.style.display = 'none';
    timeMenu.style.display = 'block';

    // Sync theme and color
    timeMenu.setAttribute('data-theme', currentTheme);
    timeMenu.setAttribute('data-color', currentColor);

    // Update location logo in time menu
    const locationLogo2 = document.getElementById('locationLogo2');
    if (locationLogo2 && currentLocation && currentLocation.logo) {
        locationLogo2.src = currentLocation.logo;
        locationLogo2.onload = function() {
            this.classList.add('loaded');
        };
        locationLogo2.onerror = function() {
            console.error('Failed to load logo in time menu:', currentLocation.logo);
            this.style.display = 'none';
        };
    } else {
        if (locationLogo2) {
            locationLogo2.style.display = 'none';
        }
    }

    // Update car info
    document.getElementById('carName').textContent = car.label || 'Unknown Car';
    document.getElementById('carCategory').textContent = `${_L('category')}: ${car.category || 'Unknown'}`;
    document.getElementById('carPrice').textContent = `${_L('price')}: $${car.price || 0}/${_L('minute_short')}`;

    // Update car icon (use config icon or fallback)
    const carIconElement = document.querySelector('.car-icon-large i');
    if (carIconElement) {
        carIconElement.className = car.icon || defaultCarIcon;
    }

    // Set current car for price calculation
    currentCar = car;
    updateTotalPrice();
}

function updateTotalPrice() {
    if (!currentCar) {
        console.warn('No current car selected for price calculation');
        return;
    }

    const timeInput = document.getElementById('rentalTime');
    const totalPriceElement = document.getElementById('totalPrice');

    if (!timeInput || !totalPriceElement) {
        console.error('Required elements not found for price calculation');
        return;
    }

    const time = parseInt(timeInput.value) || 0;
    const total = (currentCar.price || 0) * time;
    totalPriceElement.textContent = `$${total}`;
}

function rentCar() {
    if (!currentCar) {
        console.error('No car selected');
        return;
    }

    const time = parseInt(document.getElementById('rentalTime').value) || 0;

    if (time <= 0) {
        console.error('Invalid rental time');
        return;
    }

    fetch(`https://${GetParentResourceName()}/rentCar`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            car: currentCar,
            time: time
        })
    }).catch(error => {
        console.error('Error renting car:', error);
    });

    closeMenu();
}

function closeMenu() {
    fetch(`https://${GetParentResourceName()}/closeMenu`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({})
    }).catch(error => {
        console.error('Error closing menu:', error);
    });
}

// Bu callback'ler FiveM'de otomatik olarak handle edilir
// RegisterNUICallback yerine fetch kullanıyoruz