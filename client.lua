local QBCore = exports['qb-core']:GetCoreObject()
local rentalNPCs = {}
local currentRental = nil
local currentLocation = nil

-- Locale fonksiyonu
local function _L(key, ...)
    local locale = Locales[Config.Locale]
    if locale and locale[key] then
        if ... then
            return string.format(locale[key], ...)
        else
            return locale[key]
        end
    else
        return key -- Fallback olarak key'i döndür
    end
end

-- Bildirim fonksiyonu
local function ShowNotification(message, type)
    QBCore.Functions.Notify(message, type or 'primary')
end

-- NPC oluşturma
local function CreateRentalNPCs()
    for _, location in pairs(Config.RentalLocations) do
        if location.npc.enabled then
            local model = location.npc.model
            RequestModel(model)
            while not HasModelLoaded(model) do
                Wait(1)
            end

            local npc = CreatePed(4, model, location.npc.coords.x, location.npc.coords.y, location.npc.coords.z - 1.0, location.npc.coords.w, false, true)
            FreezeEntityPosition(npc, true)
            SetEntityInvincible(npc, true)
            SetBlockingOfNonTemporaryEvents(npc, true)

            if location.npc.scenario then
                TaskStartScenarioInPlace(npc, location.npc.scenario, 0, true)
            end

            -- Store NPC with location ID
            rentalNPCs[location.id] = npc

            -- Etkileşim türüne göre setup
            if Config.InteractionType == 'qb-target' then
                exports['qb-target']:AddTargetEntity(npc, {
                    options = {
                        {
                            type = "client",
                            event = "carrent:client:openMenu",
                            icon = "fas fa-car",
                            label = location.label,
                            locationId = location.id
                        }
                    },
                    distance = 3.0
                })
            end
        end
    end
end

-- Blip oluşturma
local function CreateBlips()
    for _, location in pairs(Config.RentalLocations) do
        if location.blip.enabled then
            local blip = AddBlipForCoord(location.npc.coords.x, location.npc.coords.y, location.npc.coords.z)
            SetBlipSprite(blip, location.blip.sprite)
            SetBlipDisplay(blip, 4)
            SetBlipScale(blip, location.blip.scale)
            SetBlipColour(blip, location.blip.color)
            SetBlipAsShortRange(blip, true)
            BeginTextCommandSetBlipName("STRING")
            AddTextComponentString(location.blip.label)
            EndTextCommandSetBlipName(blip)
        end
    end
end

-- UI Açma/Kapama Fonksiyonları
function ToggleRentalMenu(show)
    SetNuiFocus(show, show)
    SendNUIMessage({
        action = "toggleMenu",
        show = show
    })
end

function ShowCarMenu(locationId)
    local cars = {}
    local location = nil

    -- Find the location
    for _, loc in pairs(Config.RentalLocations) do
        if loc.id == locationId then
            location = loc
            break
        end
    end

    if not location then
        ShowNotification(_L('location_not_found'), 'error')
        return
    end

    -- Get cars for this location
    for i, car in ipairs(location.cars) do
        table.insert(cars, {
            model = car.model,
            label = car.label,
            category = car.category,
            price = car.price,
            icon = car.icon
        })
    end

    SendNUIMessage({
        action = "showCarMenu",
        cars = cars,
        location = {
            id = location.id,
            label = location.label,
            logo = location.logo and ('images/' .. location.logo) or nil,
            uiTheme = location.uiTheme,
            rentalSettings = location.rentalSettings
        },
        locale = Locales[Config.Locale]
    })
end

function ShowTimeMenu(car)
    SendNUIMessage({
        action = "showTimeMenu",
        car = car
    })
end

-- Araç kiralama menüsü
function OpenRentalMenu(locationId)
    if not locationId then
        ShowNotification(_L('invalid_location'), 'error')
        return
    end

    currentLocation = locationId
    ShowCarMenu(locationId)
    ToggleRentalMenu(true)
end

-- Interact sistemi için kontrol
local showingTextUI = false

local function ShowTextUI(text)
    showingTextUI = true
    CreateThread(function()
        while showingTextUI do
            SetTextComponentFormat("STRING")
            AddTextComponentString(text)
            DisplayText(0.5, 0.05)
            Wait(0)
        end
    end)
end

local function HideTextUI()
    showingTextUI = false
end

CreateThread(function()
    if Config.InteractionType == 'interact' then
        while true do
            local sleep = 1000
            local playerPed = PlayerPedId()
            local playerCoords = GetEntityCoords(playerPed)
            local closestLocation = nil
            local closestDistance = 999.0

            -- Find closest location
            for _, location in pairs(Config.RentalLocations) do
                if location.npc.enabled then
                    local npcCoords = vector3(location.npc.coords.x, location.npc.coords.y, location.npc.coords.z)
                    local distance = #(playerCoords - npcCoords)

                    if distance < closestDistance then
                        closestDistance = distance
                        closestLocation = location
                    end
                end
            end

            if closestLocation and closestDistance < 3.0 then
                sleep = 0
                if closestDistance < 2.0 then
                    if not showingTextUI then
                        ShowTextUI(_L('press_e_interact', closestLocation.label))
                    end

                    if IsControlJustReleased(0, 38) then -- E tuşu
                        HideTextUI()
                        OpenRentalMenu(closestLocation.id)
                    end
                else
                    if showingTextUI then
                        HideTextUI()
                    end
                end
            else
                if showingTextUI then
                    HideTextUI()
                end
            end

            Wait(sleep)
        end
    end
end)

-- Kiralık araç spawn etme
RegisterNetEvent('carrent:client:spawnCar', function(carData, rentalTime, locationId, rentalSettings)
    local location = nil

    -- Find the location
    for _, loc in pairs(Config.RentalLocations) do
        if loc.id == locationId then
            location = loc
            break
        end
    end

    if not location then
        ShowNotification(_L('location_not_found'), 'error')
        return
    end

    -- Use location's rental settings or fallback to passed settings
    local settings = rentalSettings or location.rentalSettings

    local model = carData.model
    RequestModel(model)
    while not HasModelLoaded(model) do
        Wait(1)
    end

    local vehicle = CreateVehicle(model, location.spawnPoint.x, location.spawnPoint.y, location.spawnPoint.z, location.spawnPoint.w, true, false)
    SetEntityAsMissionEntity(vehicle, true, true)
    SetVehicleNumberPlateText(vehicle, "RENTAL")

    -- Oyuncuyu araca bindir
    local playerPed = PlayerPedId()
    SetPedIntoVehicle(playerPed, vehicle, -1)

    -- Aracın anahtarını ver
    TriggerEvent("vehiclekeys:client:SetOwner", QBCore.Functions.GetPlate(vehicle))

    -- Kiralama bilgilerini sakla
    currentRental = {
        vehicle = vehicle,
        endTime = GetGameTimer() + (rentalTime * 60 * 1000),
        plate = QBCore.Functions.GetPlate(vehicle),
        locationId = locationId,
        settings = settings
    }

    ShowNotification(_L('car_rented_success', carData.label, rentalTime), 'success')

    -- Uyarı timer başlat (location settings kullanarak)
    if rentalTime > settings.warningTime then
        SetTimeout((rentalTime - settings.warningTime) * 60 * 1000, function()
            if currentRental and DoesEntityExist(currentRental.vehicle) then
                ShowNotification(_L('rental_warning', settings.warningTime), 'warning')
            end
        end)
    end

    -- Süre bitince aracı sil (location settings kullanarak)
    SetTimeout(rentalTime * 60 * 1000, function()
        if currentRental and DoesEntityExist(currentRental.vehicle) then
            if settings.deleteOnExpire then
                DeleteEntity(currentRental.vehicle)
                ShowNotification(_L('rental_expired_deleted'), 'error')
            else
                ShowNotification(_L('rental_expired'), 'warning')
            end
            currentRental = nil
        end
    end)
end)

-- Kalan süreyi kontrol etme
RegisterCommand(Config.Commands.checkTime, function()
    if not currentRental then
        ShowNotification(_L('no_rental_car'), 'error')
        return
    end

    if not DoesEntityExist(currentRental.vehicle) then
        currentRental = nil
        ShowNotification(_L('rental_car_not_found'), 'error')
        return
    end

    local remainingTime = currentRental.endTime - GetGameTimer()
    if remainingTime <= 0 then
        ShowNotification(_L('rental_time_expired'), 'error')
        return
    end

    local minutes = math.floor(remainingTime / 60000)
    local seconds = math.floor((remainingTime % 60000) / 1000)

    ShowNotification(_L('remaining_time', minutes, seconds), 'info')
end)

-- NUI Callbacks
RegisterNUICallback('rentCar', function(data, cb)
    local car = data.car
    local time = tonumber(data.time)

    if time and car and currentLocation then
        local totalPrice = car.price * time
        TriggerServerEvent('carrent:server:rentCar', car, time, totalPrice, currentLocation)
    end
    cb('ok')
end)

RegisterNUICallback('closeMenu', function(_, cb)
    ToggleRentalMenu(false)
    cb('ok')
end)

RegisterNUICallback('showTimeMenu', function(data, cb)
    ShowTimeMenu(data.car)
    cb('ok')
end)

RegisterNUICallback('showCarMenu', function(data, cb)
    local locationId = data.locationId or currentLocation
    if locationId then
        ShowCarMenu(locationId)
    end
    cb('ok')
end)

-- Event handlers
RegisterNetEvent('carrent:client:openMenu', function(data)
    local locationId = data and data.locationId or nil
    if not locationId then
        -- Find closest location as fallback
        local playerCoords = GetEntityCoords(PlayerPedId())
        local closestDistance = 999.0

        for _, location in pairs(Config.RentalLocations) do
            local npcCoords = vector3(location.npc.coords.x, location.npc.coords.y, location.npc.coords.z)
            local distance = #(playerCoords - npcCoords)

            if distance < closestDistance then
                closestDistance = distance
                locationId = location.id
            end
        end
    end

    if locationId then
        OpenRentalMenu(locationId)
    end
end)

RegisterNetEvent('carrent:client:rentalFailed', function(reason)
    ShowNotification(reason, 'error')
end)

-- Script başlatıldığında
CreateThread(function()
    CreateRentalNPCs()
    CreateBlips()

    -- UI kaynaklarını yükle
    RequestStreamedTextureDict("commonmenu", true)
    while not HasStreamedTextureDictLoaded("commonmenu") do
        Wait(1)
    end
end)

-- Kaynak durdurulduğunda temizlik
AddEventHandler('onResourceStop', function(resource)
    if resource == GetCurrentResourceName() then
        -- Clean up all NPCs
        for _, npc in pairs(rentalNPCs) do
            if DoesEntityExist(npc) then
                DeleteEntity(npc)
            end
        end

        if currentRental and DoesEntityExist(currentRental.vehicle) then
            DeleteEntity(currentRental.vehicle)
        end
        HideTextUI()
        ToggleRentalMenu(false)
    end
end)